import { group, check } from "k6";
import { CustomHeaders } from "../../core/httpx";
import {logout} from "../login";
export function listDebitCards(customHeaders: CustomHeaders) {
  return group("List debit cards", () => {
    const res = api.get(
      "/v1/cards/debit-cards?action=link&skipCache=true",
      "listDebitCards",
      customHeaders,
    );

    check(res, {
      "listDebitCards response is success": (r) => r.status === 200,
    });
    if (res.status !== 200) {
      logout(customHeaders)
      throw new Error(
        `Use Case Failure - list debit cards failed. - ${res.status} - ${res.body} - ${res.request.url}`,
      );
    }
    try {
      return res.json("data.cards");
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return cards. - ${e.message}`,
      );
    }
  });
}
