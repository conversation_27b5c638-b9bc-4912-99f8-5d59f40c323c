import { group, check } from "k6";
import { CustomHeaders } from "../../core/httpx";
import {logout} from "../login";

export function selectdepositType(type: string, customHeaders: CustomHeaders) {
  group("book certificate deposit", () => {
    const res = api.get(
      "/v1/deposits/catalog?depositType=" + type + "",
      "book certificate deposit",
      customHeaders,
    );

    check(res, {
      "is status 200": (r) => r.status === 200,
    });
  });
}

export function bookCertificateDeposits(body: any, customHeaders: CustomHeaders) {
  return group("book certificate deposit", () => {
    const res = api.post(
      `/v1/deposits`,
      body,
      "book certificate deposit",
      customHeaders,
    );
        console.log("book deposit success :-" , res)

    check(res, {
      "book deposit success": (r) => r.status === 200,
    });
    if (res.status != 200) {
      logout(customHeaders)
    }
        return res.json("data")
  });
}

export function postDepositInterestRate(body: any, customHeaders: CustomHeaders) {
  group("deposit interest rate", () => {
    const res = api.post(
      `/v1/deposits/interest-rate`,
      body,
      "deposit interest rate",
      customHeaders,
    );
    console.log("Deposit INterest Response :-" , res.status)
    check(res, {
      "deposit interest rate success": (r) => r.status === 200,
    });
    if (res.status != 200) {
      logout(customHeaders)
    }
  });
}

export function getEligibleAccount(currency: string, customHeaders: CustomHeaders) {
  return group("eligible  account", () => {
    const res = api.get(
      "/v1/accounts?currency=" +
        currency +
        "&transferTo=true&requestType=deposits&minAmount=1000",
      "list eligible account",
      customHeaders,
    );

    check(res, {
      "is status 200": (r) => r.status === 200,
    });
    if (res.status != 200) {
      logout(customHeaders)
    }
    try {
      const arrayData: any = res.json("data.accounts");
      let eligibleAccount = "";

      for (let step = 0; step < arrayData.length; step++) {
        if (arrayData[step].operations.deposit.enabled == true) {
          eligibleAccount = arrayData[step].id;
          break;
        }
      }
      return eligibleAccount;
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return accounts. - ${e.message}`,
      );
    }
  });
}
