import { group, check } from "k6";
import { CustomHeaders } from "../../core/httpx";

export function getDepositList(customHeaders: CustomHeaders) {
  return group("Deposit List", () => {
    const res = api.get(
      "/v1/deposits?depositType=TD&sortParam=currency&sortDirection=asc&page=1",
      "deposit List",
      customHeaders,
    );
    check(res, {
      "Deposit List success": (r) => r.status === 200,
    });
    try {
      return res.json("data.depositsList");
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return deposits list. - ${e.message}`,
      );
    }
  });
}

export function getDepositCatalogType(customHeaders: CustomHeaders) {
  return group("Deposit catalog type", () => {
    const res = api.get(
      "/v1/deposits/catalog?depositType=CD",
      "depositCatalogType",
      customHeaders,
    );
    check(res, {
      "Deposit catalog type success": (r) => r.status === 200,
    });
    if (res.status != 200) {
      logout(customHeaders)
    }
    try {
      return res.json("data");
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return data. - ${e.message}`,
      );
    }
  });
}

export function getDepositCatalogTenor(customHeaders: CustomHeaders) {
  return group("Deposit catalog tenor", () => {
    const res = api.get(
      "/v1/deposits/catalog?tenorRange=true&depositType=CD",
      "depositCatalogTenor",
      customHeaders,
    );
    check(res, {
      "Deposit catalog tenor success": (r) => r.status === 200,
    });
    if (res.status != 200) {
      logout(customHeaders)
    }
    try {
      return res.json("data");
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return data. - ${e.message}`,
      );
    }
  });
}

export function getDepositCatalogCurrency(customHeaders: CustomHeaders) {
  return group("Deposit catalog currency", () => {
    const res = api.get(
      "/v1/deposits/catalog?depositType=CD&currency=EGP",
      "depositCatalogCurrency",
      customHeaders,
    );
    check(res, {
      "Deposit catalog currency success": (r) => r.status === 200,
    });
    if (res.status != 200) {
      logout(customHeaders)
    }
    try {
      return res.json("data");
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return data. - ${e.message}`,
      );
    }
  });
}

export function getDepositDetails(depositID: any, customHeaders: CustomHeaders) {
  group("Deposit Details", () => {
    const body = {
      depositId: depositID,
    };
    const res = api.post(
      "/v1/deposits/deposits-per",
      body,
      "deposit Details",
      customHeaders,
    );
    check(res, {
      "Deposit Details success": (r) => r.status === 200,
    });
    if (res.status != 200) {
      logout(customHeaders)
    }
  });
}

export function getDepositUserEligibility(customHeaders: CustomHeaders) {
  return group("Deposit user eligibility", () => {
    const res = api.get(
      "/v1/deposits/user-eligibility",
      "deposit user eligibility",
      customHeaders,
    );
    check(res, {
      "Deposit user eligibility": (r) => r.status === 200,
    });
    if (res.status != 200) {
      logout(customHeaders)
    }
    try {
      return res.json("data");
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return data. - ${e.message}`,
      );
    }
  });
}

export function getDepositsTermsConditions(customHeaders: CustomHeaders) {
  return group("Deposit terms conditions", () => {
    const res = api.get(
      "/v1/deposits/terms-and-conditions",
      "deposit terms conditions",
      customHeaders,
    );
    check(res, {
      "Deposit terms and conditions success": (r) => r.status === 200,
    });
    if (res.status != 200) {
      logout(customHeaders)
    }
    try {
      return res.json("data");
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return data. - ${e.message}`,
      );
    }
  });
}
