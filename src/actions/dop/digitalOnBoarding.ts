import { group, check } from 'k6';
import { CustomHeaders } from "../../core/httpx";

export function postStep1CustomerExists(body: any, customHeaders: CustomHeaders) {
    return group("Step 1: Customer Exists", () => {
        const res = api.post("/initial", body, "step 1: customer exists", customHeaders);
        check(res, { "Step 1: Customer Exists successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function postStep3GetDfiTransactionId(body: any, customHeaders: CustomHeaders) {
    return group("Step 3: Get DFI transaction id", () => {
        const res = api.post("/new-transaction", body, "step 3: get dfi transaction id", customHeaders);
        check(res, { "Step 3: Get DFI transaction id successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function postStep3DfiEkyc(body: any, customHeaders: CustomHeaders) {
    return group("Step 3: DFI ekyc", () => {
        const res = api.post("/", body, "step 3: dfi ekyc", customHeaders);
        check(res, { "Step 3: DFI ekyc successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function postStepOptional10DfiRevoke(body: any, customHeaders: CustomHeaders) {
    return group("Step [optional - 10]: DFI revoke", () => {
        const res = api.post("/revoke", body, "step [optional - 10]: dfi revoke", customHeaders);
        check(res, { "Step [optional - 10]: DFI revoke successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function postStep2OfferingSubmit(body: any, customHeaders: CustomHeaders) {
    return group("Step 2: Offering Submit", () => {
        const res = api.post("/offering", body, "step 2: offering submit", customHeaders);
        check(res, { "Step 2: Offering Submit successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function getStep2OfferingsLoad( customHeaders: CustomHeaders) {
    return group("Step 2: Offerings load", () => {
        const res = api.get("/offering", "step 2: offerings load", customHeaders);
        check(res, { "Step 2: Offerings load successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function getStep4PersonaldeclarationLoadCopy( customHeaders: CustomHeaders) {
    return group("Step 4: Personal-declaration load Copy", () => {
        const res = api.get("/personal-declaration", "step 4: personal-declaration load copy", customHeaders);
        check(res, { "Step 4: Personal-declaration load Copy successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function postStep4PersonaldeclarationSubmit(body: any, customHeaders: CustomHeaders) {
    return group("Step 4: Personal-declaration submit", () => {
        const res = api.post("/personal-declaration", body, "step 4: personal-declaration submit", customHeaders);
        check(res, { "Step 4: Personal-declaration submit successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function getStep5PersonaldeclarationExtendLoad( customHeaders: CustomHeaders) {
    return group("Step 5: Personal-declaration extend load", () => {
        const res = api.get("/personal-declaration-extension", "step 5: personal-declaration extend load", customHeaders);
        check(res, { "Step 5: Personal-declaration extend load successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function postStep5PersonaldeclarationExtendSubmit(body: any, customHeaders: CustomHeaders) {
    return group("Step 5: Personal-declaration extend submit", () => {
        const res = api.post("/personal-declaration-extension", body, "step 5: personal-declaration extend submit", customHeaders);
        check(res, { "Step 5: Personal-declaration extend submit successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function getStep6WorkdetailsLoad(customHeaders: CustomHeaders) {
    return group("Step 6: Work-details load", () => {
        const res = api.get("/work-details", "step 6: work-details load", customHeaders);
        check(res, { "Step 6: Work-details load successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function postStep6WorkdetailsSubmit(body: any, customHeaders: CustomHeaders) {
    return group("Step 6: Work-details submit", () => {
        const res = api.post("/work-details", body, "step 6: work-details submit", customHeaders);
        check(res, { "Step 6: Work-details submit successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function getStep7AddressdataLoad(customHeaders: CustomHeaders) {
    return group("Step 7: Address-data load", () => {
        const res = api.get("/address-data", "step 7: address-data load", customHeaders);
        check(res, { "Step 7: Address-data load successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function postStep7AddressSubmit(body: any, customHeaders: CustomHeaders) {
    return group("Step 7: Address submit", () => {
        const res = api.post("/address-data", body, "step 7: address submit", customHeaders);
        check(res, { "Step 7: Address submit successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function getStep11AccountCreationReviewLoad(customHeaders: CustomHeaders) {
    return group("Step 11: Account Creation Review load", () => {
        const res = api.get("/customer-creation-review", "step 11: account creation review load", customHeaders);
        check(res, { "Step 11: Account Creation Review load successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function postStep10AuthenticationSubmit(body: any, customHeaders: CustomHeaders) {
    return group("Step 10: Authentication submit", () => {
        const res = api.post("/authentication", body, "step 10: authentication submit", customHeaders);
        check(res, { "Step 10: Authentication submit successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function getStep10AuthenticationLoad( customHeaders: CustomHeaders) {
    return group("Step 10: Authentication load", () => {
        const res = api.get("/authentication", "step 10: authentication load", customHeaders);
        check(res, { "Step 10: Authentication load successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function getStep9CardIssuanceLoad(customHeaders: CustomHeaders) {
    return group("Step 9: Card Issuance load", () => {
        const res = api.get("/card-issuance", "step 9: card issuance load", customHeaders);
        check(res, { "Step 9: Card Issuance load successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function postStep9CardIssuanceSubmit(body: any, customHeaders: CustomHeaders) {
    return group("Step 9: Card Issuance submit", () => {
        const res = api.post("/card-issuance", body, "step 9: card issuance submit", customHeaders);
        check(res, { "Step 9: Card Issuance submit successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function getStep8RequestReviewLoad(customHeaders: CustomHeaders) {
    return group("Step 8: Request Review load", () => {
        const res = api.get("/request-review", "step 8: request review load", customHeaders);
        check(res, { "Step 8: Request Review load successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function getStepOptionalRequireAttachmentsLoad(customHeaders: CustomHeaders) {
    return group("Step [Optional]: Require Attachments load", () => {
        const res = api.get("/required-attachments", "step [optional]: require attachments load", customHeaders);
        check(res, { "Step [Optional]: Require Attachments load successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function postStepOptionalRequireAttachmentsSubmit(body: any, customHeaders: CustomHeaders) {
    return group("Step [Optional]: Require Attachments submit", () => {
        const res = api.post("/required-attachments", body, "step [optional]: require attachments submit", customHeaders);
        check(res, { "Step [Optional]: Require Attachments submit successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function postStepOptionalRestoreRequest(body: any, customHeaders: CustomHeaders) {
    return group("Step [Optional]: Restore request", () => {
        const res = api.post("/request", body, "step [optional]: restore request", customHeaders);
        check(res, { "Step [Optional]: Restore request successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function postStepOptionalRestoreConfirm(body: any, customHeaders: CustomHeaders) {
    return group("Step [Optional]: Restore confirm", () => {
        const res = api.post("/confirm", body, "step [optional]: restore confirm", customHeaders);
        check(res, { "Step [Optional]: Restore confirm successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function postStepOptionalUploadAttachment(body: any, customHeaders: CustomHeaders) {
    return group("Step [Optional]: Upload Attachment", () => {
        const res = api.post("/upload", body, "step [optional]: upload attachment", customHeaders);
        check(res, { "Step [Optional]: Upload Attachment successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function getStepOptionalUploadList(body: any, customHeaders: CustomHeaders) {
    return group("Step [Optional]: Upload List", () => {
        const res = api.get("/", "step [optional]: upload list", customHeaders);
        check(res, { "Step [Optional]: Upload List successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

// export function deleteStepOptionalDeleteAttachment(body: any, customHeaders: CustomHeaders) {
//     return group("Step [Optional]: Delete Attachment", () => {
//         const res = api.delete("/delete", body, "step [optional]: delete attachment", customHeaders);
//         check(res, { "Step [Optional]: Delete Attachment successful": (r) => r.status >= 200 && r.status < 300 });
//         return res;
//     });
// }

export function postCrm(body: any, customHeaders: CustomHeaders) {
    return group("crm", () => {
        const res = api.post("/notify/invalid-documents", body, "crm", customHeaders);
        check(res, { "crm successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

