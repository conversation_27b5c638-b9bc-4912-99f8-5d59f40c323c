import { group, check } from "k6";
import { CustomHeaders } from "../../core/httpx";

export function listRecentTransfers(customHeaders: CustomHeaders) {
  group("List recent transfers", () => {
    const res = api.get(`/v1/transfers?page=1`, "listRecentTransfers", customHeaders);
    check(res, {
      "list recent transfers status 200": (r) => r.status === 200,
    });
  });
}

export function listScheduledTransfers(customHeaders: CustomHeaders) {
  group("List scheduled transfers", () => {
    const res = api.get(
      `/v1/transfers?page=1&type=schedule`,
      "listScheduleTransfers",
      customHeaders,
    );
    check(res, {
      "list scheduled transfers status 200": (r) => r.status === 200,
    });
  });
}

export function listHomeTransfers(customHeaders: CustomHeaders) {
  group("List home transfers", () => {
    const res = api.get(`/v1/transfers/home`, "listHomeTransfers", customHeaders);
    check(res, {
      "list home transfers status 200": (r) => r.status === 200,
    });
    if (res.status != 200) {
      logout(customHeaders)
    }
  });
}
