import { group, check } from "k6";
import { CustomHeaders } from "../../core/httpx";
import {logout} from "../login";

export function listRelatedAccounts(customHeaders: CustomHeaders) {
  group("List related accounts", () => {
    const res = api.get(
      `/v1/related-accounts/list`,
      "listRelatedAccounts",
      customHeaders,
    );
    check(res, {
      "list related accounts status 200": (r) => r.status === 200,
    });
    if (res.status != 200) {
      logout(customHeaders)
    }
  });
}
