import { group, check } from "k6";
import { CustomHeaders } from "../../core/httpx";
import {logout} from "../login";

export function createInsdeCibTransfer(body: any, customHeaders: CustomHeaders) {
  group("Transfer to another CIB account", () => {
    const res = api.post(
      `/v1/transfers`,
      body,
      "createInsideCibTransfer",
      customHeaders,
    );
    check(res, {
      "transfer inside CIB success": (r) => r.status === 200,
    });
    if (res.status !== 200) {
      logout(customHeaders)
      throw new Error(
        `Use Case Failure - inside CIB transfer failed. - ${res.status} - ${res.body} - ${res.request.url}`,
      );
    }
  });
}
export function createOutsideCibTransfer(body: any, customHeaders: CustomHeaders) {
  group("Transfer to outside  account", () => {
    const res = api.post(
      `/v1/transfers`,
      body,
      "createOutSideCibTransfer",
      customHeaders,
    );
    check(res, {
      "transfer outside CIB success": (r) => r.status === 200,
    });
    if (res.status !== 200) {
      logout(customHeaders)
      throw new Error(
        `Use Case Failure - outside CIB transfer failed. - ${res.status} - ${res.body} - ${res.request.url}`,
      );
    }
  });
}

export function createCreditCardTranfer(body: any, customHeaders: CustomHeaders) {
  group("Transfer to credit card", () => {
    const res = api.post(
      `/v1/transfers`,
      body,
      "createCreditCardTransfer",
      customHeaders,
    );
    check(res, {
      "transfer to credit card success": (r) => r.status === 200,
    });
    if (res.status !== 200) {
      logout(customHeaders)
      throw new Error(
        `Use Case Failure - credit card transfer failed. - ${res.status} - ${res.body} - ${res.request.url}`,
      );
    }
  });
}
