import { group, check } from "k6";
import { CustomHeaders } from "../../core/httpx";
import {logout} from "../login";

export function getBeneficiaries(customHeaders: CustomHeaders) {
  group("get beneficiaries list", () => {
    const res = api.get(
      `/v1/beneficiaries?page=1&type=local`,
      "getbeneficiarieslist",
      customHeaders,
    );
    check(res, {
      "lis beneficiaries": (r) => r.status === 200,
    });
    if (res.status != 200) {
      logout(customHeaders)
    }
  });
}

export function getBeneficiariesCategories(customHeaders: CustomHeaders) {
  group("get beneficiaries  categories list", () => {
    const res = api.get(
      `/v1/beneficiaries/categories`,
      "getbeneficiariescategorieslist",
      customHeaders,
    );
    check(res, {
      "lis beneficiaries": (r) => r.status === 200,
    });
    if (res.status != 200) {
      logout(customHeaders)
    }
  });
}
