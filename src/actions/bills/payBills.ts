import { group, check } from "k6";
import { CustomHeaders } from "../../core/httpx";

export function billPaymentLookup(body: any, customHeaders: CustomHeaders) {
  return group("Billers", () => {
    const res = api.post(
      "/v1/bill-payments/lookup",
      body,
      "Bills Lookup ",
      customHeaders,
    );

    check(res, {
      "Bills Lookup success": (r) => r.status === 200,
    });
    try {
      return res.json("data.billSpecific");
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return bills. - ${e.message}`,
      );
    }
  });
}

export function billPaymentConfirmation(body: any, customHeaders: CustomHeaders) {
  return group("Billpayment confirmation", () => {
    const res = api.post(
      "/v1/bill-payments/confirm",
      body,
      "Bill payment confirmation ",
      customHeaders,
    );

    check(res, {
      "Bill payment Confirtmation success": (r) => r.status === 200,
    });
    try {
      return res.json("data.requestId");
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return request id. - ${e.message}`,
      );
    }
  });
}

export function billPay(body: any, customHeaders: CustomHeaders) {
  group("Bill payment pay  ", () => {
    const res = api.post(
      "/v1/bill-payments/pay",
      body,
      "Bill payment confirmation ",
      customHeaders,
    );
    check(res, {
      "Bill payment success": (r) => r.status === 200,
    });
    if (res.status != 200) {
      logout(customHeaders)
    }
  });
}
