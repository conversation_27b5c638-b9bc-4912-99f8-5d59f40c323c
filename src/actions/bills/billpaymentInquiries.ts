import { group, check } from "k6";
import { CustomHeaders } from "../../core/httpx";
import {logout} from "../login";

export function getBillPaymentGroups(customHeaders: CustomHeaders) {
  group("Bill Payment Groups", () => {
    const res = api.get(
      "/v1/bill-payments/groups",
      "bill paymen groups",
      customHeaders,
    );

    check(res, {
      "bill payment success": (r) => r.status === 200,
    });
    if (res.status != 200) {
      logout(customHeaders)
    }
  });
}

export function getFavoritesBills(customHeaders: CustomHeaders) {
  group("Favorites Bills", () => {
    const res = api.get("/v1/bill-payments", "Favorites Bills", customHeaders);
    check(res, {
      "Favorites bills success": (r) => r.status === 200,
    });
  });
}

export function getBillers(customHeaders: CustomHeaders) {
  group("Billers ", () => {
    const res = api.get(
      "/v1/bill-payments/groups/113/billers",
      "Billers ",
      customHeaders,
    );

    check(res, {
      "get Billers success": (r) => r.status === 200,
    });
  });
}

export function getBillPaymentLookup(customHeaders: CustomHeaders) {
  group("Billers ", () => {
    const res = api.get(
      "/v1/bill-payments/groups/113/billers",
      "Billers ",
      customHeaders,
    );

    check(res, {
      "bill payments lookup success": (r) => r.status === 200,
    });
  });
}
