import { loginWithBiometric, logout} from "../actions/login";
import exec from "k6/execution";

import { sleep } from "k6";
import { fail } from "k6";
import { callHomepage } from "../actions/homepage";
import { getTargetVus } from "../core/helpers/getTargetVus";
import {
    getNextBiometricUser,
    //addUserToExcluded,
} from "../core/helpers/getExcludedArray";
import {BiometricUsers} from "../typings/globals";

const waitTimeAfterLogin = 30;
const waitTimeAfterVu = 200;

export function BiometricLoginExec() {
    const userData: BiometricUsers = getNextBiometricUser(exec.vu.idInTest, globalThis.BiometricLoginUsers);
    console.log("User Data :-" , userData)
    let customHeaders: any = null;
    try {
        if (!userData) {
            throw new Error(
                `User with id ${exec.vu.idInTest} has invalid username or password. Data file: accountsSubUsers`,
            );
        }
        const token = loginWithBiometric(userData);
        sleep(waitTimeAfterLogin);
        if (token) {
            customHeaders = {token, deviceId : userData.data.deviceId,
                login_auth_key : 'mobile',
                'app-version' : 'test' }
            callHomepage(customHeaders);
            logout(customHeaders);
            console.log(`Good Test data: ${exec.scenario.name}\t ${userData.user}`);
        } else {
            throw new Error("Authentication Failure - The user was not logged in");
        }
    } catch (error: any) {
        if (
            error.message.includes("Authentication Failure") ||
            error.message.includes("Use Case Failure")
        ) {
            console.log(
                `Bad test data: ${exec.scenario.name}\t ${userData.user} - ${error?.message} - ${error?.status}`,
            );
        }
        //addUserToExcluded(user);
        sleep(waitTimeAfterVu * 3);
        fail(`Unexpected error: ${error?.message}`);
    } finally {
        if (customHeaders) {
            logout(customHeaders);
        }
        sleep(waitTimeAfterVu);
    }
}

export function BiometricLogin(
    type: "load" | "stress" | "endurance" | "smoke" | "shock",
) {
    const targetVUs = getTargetVus(type, "BiometricLogin");
    switch (type) {
        case "load":
            return {
                executor: "ramping-vus",
                startVUs: 0,
                gracefulRampDown: "600s",
                stages: [
                    { duration: "15m", target: targetVUs },
                    { duration: "50m", target: targetVUs },
                ],
                exec: BiometricLoginExec.name,
            };
        case "endurance":
            return {
                executor: "ramping-vus",
                startVUs: 0,
                gracefulRampDown: "300s",
                stages: [
                    { duration: "1h", target: targetVUs },
                    { duration: "12h", target: targetVUs },
                    { duration: "1h", target: 0 },
                ],
                exec: BiometricLoginExec.name,
            };
        case "smoke":
            return {
                executor: "per-vu-iterations",
                vus: 1,
                iterations: 1,
                exec: BiometricLoginExec.name,
            };
        case "shock":
            return {
                executor: "ramping-vus",
                startVUs: 0,
                gracefulRampDown: "600s",
                stages: [
                    { duration: "5m", target: targetVUs },
                    { duration: "5m", target: targetVUs },
                ],
                exec: BiometricLoginExec.name,
            };
        default:
            throw new Error("Invalid type");
    }
}
