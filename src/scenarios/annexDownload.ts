import { fail, sleep } from "k6";
import { login, logout } from "../actions/login";
import { callHomepage } from "../actions/homepage";
import { getTargetVus } from "../core/helpers/getTargetVus";
import { getNextUser } from "../core/helpers/getExcludedArray";
import {
    listAccounts,
    listAccountsForBookDeposits
} from "../actions/accounts";
import {
    createAccount,
    getAccountsCatalog, getDownloadAnnexForAccounts,

    getDownloadAnnexForDeposit, getValidateOpenNewAccount,

} from "../actions/annex";
import {
    bookCertificateDeposits,
    getDepositCatalogCurrency,
    getDepositCatalogTenor,
    getDepositCatalogType,
    getDepositList,
    getDepositsTermsConditions,
    getDepositUserEligibility,
    postDepositInterestRate
} from "../actions/deposits";
import { getEligibleBookDepositAccount } from "../actions/accounts/accounts-utils";
import exec from "k6/execution";

const waitTimeAfterLogin = 30;
const waitTimeAfterVu = 200;
/**
 * Main execution function for the Annex Download scenario
 */
export function annexDownloadExec() {
    const user: any = getNextUser(exec.vu.idInTest, globalThis.annexSubUsers);

    try {
        if (!user.username || !user.password) {
            console.log(`Warning: User with id ${exec.vu.idInTest} has invalid username or password. Data file: annexSubUsers`);
            // Skip this iteration but don't fail the test
            return;
        }

        const deviceId = `${user.username}_deviceId5`;
        const token = login(user.username, user.password, deviceId);

        sleep(waitTimeAfterLogin);

        if (token) {
            const customHeaders = { token, deviceId };

            // Call homepage to simulate user navigation
            callHomepage(customHeaders);

            // // Account Annex Flow
            // const validateOpenNewAccount = getValidateOpenNewAccount(customHeaders) as any;
            //
            // if (validateOpenNewAccount?.operations?.openNewAccount?.enabled) {
            //     getAccountsCatalog(customHeaders);
            //     const accountsListData: any = listAccounts(customHeaders);
            //
            //     if (accountsListData?.accounts && accountsListData.accounts.length > 0) {
            //         const accountId = accountsListData.accounts[0].id;
            //         const openAccountPayload = {
            //             "accountType": "Savings",
            //             "accountSubType": "Savers",
            //             "currency": "USD",
            //             "tcFlag": true,
            //             "tenor": "Daily",
            //             "signatureFromAccountId": accountId
            //         };
            //
            //         const newAccount = createAccount(openAccountPayload, customHeaders) as any;
            //         if ((newAccount as any)?.accountNumber) {
            //             getDownloadAnnexForAccounts((newAccount as any).accountNumber, customHeaders);
            //         }
            //     }
            // }

            // Deposit Annex Flow
            getDepositList(customHeaders);
            getDepositCatalogType(customHeaders);
            getDepositCatalogCurrency(customHeaders);
            getDepositCatalogTenor(customHeaders);
            getDepositUserEligibility(customHeaders);
            getDepositsTermsConditions(customHeaders);

            const listOfTransferAccounts: any = listAccountsForBookDeposits(customHeaders);
            const eligibleLocalTransferAccount = getEligibleBookDepositAccount(
                listOfTransferAccounts,
            );

            if (eligibleLocalTransferAccount) {
                const bookBody = {
                    type: "CD",
                    frequency: "Floating – Monthly - Corridor Linked",
                    currency: "EGP",
                    tenor: "3 Years - 23.75%(1.25% below Corridor rate)",
                    amount: 1000,
                    maturity: "Close on maturity",
                    debitAccountId: eligibleLocalTransferAccount.id,
                    principalCreditAccountId: eligibleLocalTransferAccount.id,
                    interestCreditAccountId: eligibleLocalTransferAccount.id,
                    code: "EGP.1000.01M.36M.V",
                };

                const interestBody = {
                    type: "CD",
                    frequency: "Floating – Monthly - Corridor Linked",
                    currency: "EGP",
                    tenor: "3 Years - 23.75%(1.25% below Corridor rate)",
                    amount: 1000,
                    maturity: "Close on maturity",
                    debitAccountId: eligibleLocalTransferAccount.id,
                    principalCreditAccountId: eligibleLocalTransferAccount.id,
                    interestCreditAccountId: eligibleLocalTransferAccount.id,
                    code: "EGP.1000.01M.36M.V",
                };

                postDepositInterestRate(interestBody, customHeaders);
              const bookCertificate =  bookCertificateDeposits(bookBody, customHeaders) as any;
                console.log("Book Deposit :-" , bookCertificate)
                // Download annex for the created deposit (using test ID)
                if (bookCertificate?.depositId){
                    getDownloadAnnexForDeposit(bookCertificate.depositId , customHeaders);
                }
            }

            // Logout to clean up the session
            logout(customHeaders);
        } else {
            console.log("Authentication Failure - The user was not logged in");
        }
    } catch (error: any) {
        if (
            error.message.includes("Authentication Failure") ||
            error.message.includes("Use Case Failure")
        ) {
            console.log(
                `Bad test data: ${exec.scenario.name}\t ${user.username} - ${error?.message} - ${error?.status}`,
            );
        }
        sleep(waitTimeAfterVu * 3);
        fail(`Unexpected error: ${error?.message}`);
    } finally {
        sleep(waitTimeAfterVu);
    }
}

/**
 * Configures the load test scenario based on the specified type
 * @param type The type of load test to configure
 * @returns The load test configuration
 */
export function annexDownload(
    type: "load" | "stress" | "endurance" | "smoke" | "shock",
) {
    const targetVUs = getTargetVus(type, "annexDownload");

    switch (type) {
        case "load":
            return {
                executor: "ramping-vus",
                startVUs: 1,
                gracefulRampDown: "600s",
                stages: [
                    { duration: "15m", target: targetVUs },
                    { duration: "50m", target: targetVUs },
                ],
                exec: annexDownloadExec.name,
            };
        case "endurance":
            return {
                executor: "ramping-vus",
                startVUs: 1,
                stages: [
                    { duration: "1h", target: targetVUs > 0 ? targetVUs : 5 },
                    { duration: "12h", target: targetVUs > 0 ? targetVUs : 5 },
                    { duration: "1h", target: 0 },
                ],
                exec: annexDownloadExec.name,
            };
        case "smoke":
            return {
                executor: "per-vu-iterations",
                vus: 2,
                iterations: 1,
                exec: annexDownloadExec.name,
            };
        case "shock":
            return {
                executor: "ramping-vus",
                startVUs: 1,
                gracefulRampDown: "600s",
                stages: [
                    { duration: "5m", target: targetVUs > 0 ? targetVUs : 5 },
                    { duration: "5m", target: targetVUs > 0 ? targetVUs : 5 },
                ],
                exec: annexDownloadExec.name,
            };
        default:
            throw new Error("Invalid type");
    }
}
