import { login, logout } from "../actions/login";
import exec from "k6/execution";

import { listDebitCards } from "../actions/debitCards/listDeditCards"; // Import for debit card listing
import { fail, sleep } from "k6";
import { callHomepage } from "../actions/homepage";
import {
  //addUserToExcluded,
  getNextUser,
} from "../core/helpers/getExcludedArray";
import { getTargetVus } from "../core/helpers/getTargetVus";

const waitTimeAfterLogin = 30;
const waitTimeAfterVu = 105;

export function debitCardListExec() {
  const user: any = getNextUser(exec.vu.idInTest, globalThis.debitCardSubUsers);
  // Simplified function for listing debit cards only
  let customHeaders: any = null;
  try {
    if (!user.username || !user.password) {
      throw new Error(
        `User with id ${exec.vu.idInTest} has invalid username or password. Data file: debitCardSubUsers`,
      );
    }
    const deviceId = `${user.username}_deviceId5`
    const token = login(user.username, user.password, deviceId);
    sleep(waitTimeAfterLogin);
    if (token) {
      customHeaders = {token, deviceId}
      callHomepage(customHeaders); // Call homepage after login
      var cardList: any = listDebitCards(customHeaders); // List debit cards using customHeaders
      if (!cardList || cardList.length === 0) {
        console.warn("No debit cards found for user: ", user.username);
      }
    } else {
      throw new Error("Authentication Failure - The user was not logged in");
    }
  } catch (error: any) {
    if (
      error.message.includes("Authentication Failure") ||
      error.message.includes("Use Case Failure")
    ) {
      console.log(
        `Bad test data: ${exec.scenario.name}\t ${user.username} - ${error?.message} - ${error?.status}`,
      );
    }
    //addUserToExcluded(user);
    sleep(waitTimeAfterVu * 3);
    fail(`Unexpected error: ${error?.message}`);
  } finally {
    if (customHeaders) {
      logout(customHeaders);
    }
    sleep(waitTimeAfterVu);
  }
}

export function debitCardList(
  type: "load" | "stress" | "endurance" | "smoke" | "shock",
) {
  const targetVUs = getTargetVus(type, "debitCardList");
  switch (type) {
    case "load":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "15m", target: targetVUs },
          { duration: "50m", target: targetVUs },
        ],
        exec: debitCardListExec.name, // Execute the debitCardListExec function
      };
    case "endurance":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        stages: [
          { duration: "1h", target: targetVUs },
          { duration: "12h", target: targetVUs },
          { duration: "1h", target: 0 },
        ],
        exec: debitCardListExec.name, // Execute the debitCardListExec function
      };
    case "smoke":
      return {
        executor: "per-vu-iterations",
        vus: 1,
        iterations: 1,
        exec: debitCardListExec.name, // Execute the debitCardListExec function
      };
    case "shock":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "5m", target: targetVUs },
          { duration: "5m", target: targetVUs },
        ],
        exec: debitCardListExec.name, // Execute the debitCardListExec function
      };
    default:
      throw new Error("Invalid type");
  }
}
