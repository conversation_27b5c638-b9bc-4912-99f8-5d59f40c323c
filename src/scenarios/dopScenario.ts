import { sleep } from 'k6';
import {callHomepage} from "../actions/homepage";
import {
    getStep10AuthenticationLoad,
    getStep11AccountCreationReviewLoad,
    getStep2OfferingsLoad,
    getStep4PersonaldeclarationLoadCopy,
    getStep5PersonaldeclarationExtendLoad,
    getStep6WorkdetailsLoad,
    getStep7AddressdataLoad,
    getStep8RequestReviewLoad,
    getStep9CardIssuanceLoad,
    getStepOptionalRequireAttachmentsLoad, getStepOptionalUploadList, postCrm,
    postStep10AuthenticationSubmit,
    postStep1CustomerExists,
    postStep2OfferingSubmit,
    postStep3DfiEkyc,
    postStep3GetDfiTransactionId,
    postStep4PersonaldeclarationSubmit,
    postStep5PersonaldeclarationExtendSubmit,
    postStep6WorkdetailsSubmit,
    postStep7AddressSubmit,
    postStep9CardIssuanceSubmit,
    postStepOptional10DfiRevoke,
    postStepOptionalRequireAttachmentsSubmit,
    postStepOptionalRestoreConfirm,
    postStepOptionalRestoreRequest, postStepOptionalUploadAttachment
} from "../actions/dop/digitalOnBoarding";

export function dobExec() {
    // Login and get session token
    const deviceId = 'test-device-' + Math.random().toString(36).substring(2, 10);
    const customHeaders = {  deviceId };

    // Navigate to homepage
    callHomepage(customHeaders);

    // Primary flow
    postStep1CustomerExists({
        "nationalId": "28707060120455",
        "firstName": "HALAA",
        "secondName": "HALAA",
        "thirdName": "HALAA",
        "fourthName": "HALAA",
        "email": "<EMAIL>"
    }, customHeaders);
    sleep(1);
    postStep3GetDfiTransactionId({}, customHeaders);
    sleep(1);
    postStep3DfiEkyc({}, customHeaders);
    sleep(1);
    postStepOptional10DfiRevoke({}, customHeaders);
    sleep(1);
    postStep2OfferingSubmit({
        "livesInEgypt": true,
        "offeringId": "premium-package",
        "branchCode": "*********"
    }, customHeaders);
    sleep(1);
    getStep2OfferingsLoad( customHeaders);
    sleep(1);
    getStep4PersonaldeclarationLoadCopy( customHeaders);
    sleep(1);
    postStep4PersonaldeclarationSubmit({}, customHeaders);
    sleep(1);
    getStep5PersonaldeclarationExtendLoad( customHeaders);
    sleep(1);
    postStep5PersonaldeclarationExtendSubmit({
        "prominentPosition": "NONE"
    }, customHeaders);
    sleep(1);
    getStep6WorkdetailsLoad(customHeaders);
    sleep(1);
    postStep6WorkdetailsSubmit({
        "workSameAsID": true,
        "employmentStatus": "employed",
        "workAddressDetails": "ZAHRAA EL MAADI - smart village - giza - cairo",
        "employerName": "hello world Company",
        "businessSector": "RETAIL_CRM",
        "occupation": "170",
        "annualIncomeRange": "250"
    }, customHeaders);
    sleep(1);
    getStep7AddressdataLoad(customHeaders);
    sleep(1);
    postStep7AddressSubmit({
        "addressSameAsID": true,
        "region": "265",
        "district": "59",
        "addressDetails": "Street 1",
        "workAddressDetails": "MASR EL GEDEDA - zahraa el maadi - smart village - giza - cairo",
        "proofOfAddressType": "UTILITY_BILL",
        "mailingAddressType": "OTHER",
        "mailingRegion": "265",
        "mailingDistrict": "59",
        "mailingAddressDetails": "SAN STEFANOMASR EL GEDEDA - zahraa el maadi - smart village - giza - cairo"
    }, customHeaders);
    sleep(1);
    getStep11AccountCreationReviewLoad(customHeaders);
    sleep(1);
    postStep10AuthenticationSubmit({}, customHeaders);
    sleep(1);
    getStep10AuthenticationLoad( customHeaders);
    sleep(1);
    getStep9CardIssuanceLoad(customHeaders);
    sleep(1);
    postStep9CardIssuanceSubmit({
        "nameIndex": 0
    }, customHeaders);
    sleep(1);
    getStep8RequestReviewLoad(customHeaders);
    sleep(1);
    getStepOptionalRequireAttachmentsLoad(customHeaders);
    sleep(1);
    postStepOptionalRequireAttachmentsSubmit({}, customHeaders);
    sleep(1);
    postStepOptionalRestoreRequest({
        "nationalId": "**************"
    }, customHeaders);
    sleep(1);
    postStepOptionalRestoreConfirm({
        "otp": "546002"
    }, customHeaders);
    sleep(1);
    postStepOptionalUploadAttachment({
        "test": "fff",
        "fileName": "1",
        "attachmentType": "PROOF_OF_INCOME"
    }, customHeaders);
    sleep(1);
    getStepOptionalUploadList({
        "test": "fff",
        "fileName": "1",
        "attachmentType": "BIRTH_CERTIFICATE"
    }, customHeaders);
    sleep(1);
    // deleteStepOptionalDeleteAttachment({
    //     "attachmentType": "BIRTH_CERTIFICATE"
    // }, customHeaders);
    sleep(1);
    postCrm({
        "requestId": "30805167993972",
        "invalidDocumentTypes": [
        ]
    }, customHeaders);

    // Logout to clean up session
}
