import { fail, sleep } from "k6";
import { getFxRate } from "../actions/accounts";
import { getDepositList, getDepositDetails } from "../actions/deposits";

import { login, logout } from "../actions/login";

import exec from "k6/execution";
import { callHomepage } from "../actions/homepage";
import {
  //addUserToExcluded,
  getNextUser,
} from "../core/helpers/getExcludedArray";
import { getTargetVus } from "../core/helpers/getTargetVus";

const waitTimeAfterLogin = 30;
const waitTimeAfterVu = 180;

export function depositDetailsExec() {
  const user: any = getNextUser(
    exec.vu.idInTest,
    globalThis.depositsListSubUsers,
  );
  try {
    if (!user.username || !user.password) {
      throw new Error(
        `User with id ${exec.vu.idInTest} has invalid username or password. Data file: depositsListSubUsers`,
      );
    }
    const deviceId = `${user.username}_deviceId5`
    const token = login(user.username, user.password, deviceId);
    sleep(waitTimeAfterLogin);
    if (token) {
      const customHeaders = {token, deviceId}
      callHomepage(customHeaders);
      getFxRate(customHeaders);
      const depositListData: any = getDepositList(customHeaders);
      if (depositListData.length === 0) {
        logout(customHeaders);
        throw new Error("Use Case Failure - The user doesnot have deposits");
      }
      const deposittId = depositListData[0].id;
      getDepositDetails(deposittId, customHeaders);
      logout(customHeaders);
    } else {
      fail("The user was not logged in");
    }
  } catch (error: any) {
    if (
      error.message.includes("Authentication Failure") ||
      error.message.includes("Use Case Failure")
    ) {
      console.log(
        `Bad test data: ${exec.scenario.name}\t ${user.username} - ${error?.message} - ${error?.status}`,
      );
    }
    //addUserToExcluded(user);
    sleep(waitTimeAfterVu * 3);
    fail(`Unexpected error: ${error?.message}`);
  } finally {
    sleep(waitTimeAfterVu);
  }
}

export function depositDetails(
  type: "load" | "stress" | "endurance" | "smoke" | "shock",
) {
  const targetVUs = getTargetVus(type, "depositDetails");
  switch (type) {
    case "load":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "15m", target: targetVUs },
          { duration: "50m", target: targetVUs },
        ],
        exec: depositDetailsExec.name,
      };
    case "endurance":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        stages: [
          { duration: "1h", target: targetVUs },
          { duration: "12h", target: targetVUs },
          { duration: "1h", target: 0 },
        ],
        exec: depositDetailsExec.name,
      };
    case "smoke":
      return {
        executor: "per-vu-iterations",
        vus: 1,
        iterations: 1,
        exec: depositDetailsExec.name,
      };
    case "shock":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "5m", target: targetVUs },
          { duration: "5m", target: targetVUs },
        ],
        exec: depositDetailsExec.name,
      };
    default:
      throw new Error("Invalid type");
  }
}
