import { listAccountsForLocalTransfer, getFxRate } from "../actions/accounts";
import {
  createOutsideCibTransfer,
  getBeneficiaries,
  getBeneficiariesCategories,
} from "../actions/createTransfer";
import { login, logout } from "../actions/login";

import exec from "k6/execution";
import { fail, sleep } from "k6";
import { getEligibleLocalTransferAccount } from "../actions/accounts/accounts-utils";
import { callHomepage } from "../actions/homepage";
import {
  //addUserToExcluded,
  getNextUser,
} from "../core/helpers/getExcludedArray";
import { getTargetVus } from "../core/helpers/getTargetVus";

const waitTimeAfterLogin = 90;
const waitTimeAfterVu = 123;

export function createTransferOutsideCIBExec() {
  const user: any = getNextUser(
    exec.vu.idInTest,
    globalThis.transfersOutsideCibSubUsers,
  );
  try {
    if (!user.username || !user.password) {
      throw new Error(
        `User with id ${exec.vu.idInTest} has invalid username or password. Data file: transfersOutsideCibSubUsers`,
      );
    }
    const deviceId = `${user.username}_deviceId5`
    const token = login(user.username, user.password, deviceId);
    if (token) {
      const customHeaders = {token, deviceId}
      callHomepage(customHeaders);
      sleep(waitTimeAfterLogin);
      const listOfTransferAccounts: any = listAccountsForLocalTransfer(customHeaders);
      const eligibleLocalTransferAccount = getEligibleLocalTransferAccount(
        listOfTransferAccounts,
      );
      if (!eligibleLocalTransferAccount) {
        logout(customHeaders);
        throw new Error(
          "Use Case Failure - The user doesnt hold an eligible account",
        );
      }
      const transferBody = {
        description: "test desc",
        name: "Test Test",
        feesChargesOn: "shared",
        sender: {
          accountNumber: eligibleLocalTransferAccount.accountNumber,
          currency: eligibleLocalTransferAccount.currency,
        },
        recipient: {
          name: "Test Test",
          accountNumber: "*************",
          currency: "EGP",
          address: "Cairo",
          type: "domestic",
          country: "EG",
          bank: {
            swiftCode: "EBBKEGCXXXX",
            country: "EG",
            name: "HSBC BANK EGYPT",
          },
          amount: {
            amount: 10,
            currency: "EGP",
          },
        },
        beneficiaryType: "new",
        otp: "123455",
        disclaimer: true,
      };
      getBeneficiaries(customHeaders);
      getBeneficiariesCategories(customHeaders);
      getFxRate(customHeaders);
      createOutsideCibTransfer(transferBody, customHeaders);
      logout(customHeaders);
    } else {
      throw new Error("Authentication Failure - The user was not logged in");
    }
  } catch (error: any) {
    if (
      error.message.includes("Authentication Failure") ||
      error.message.includes("Use Case Failure")
    ) {
      console.log(
        `Bad test data: ${exec.scenario.name}\t ${user.username} - ${error?.message} - ${error?.status}`,
      );
    }
    //addUserToExcluded(user);
    sleep(waitTimeAfterVu * 3);
    fail(`Unexpected error: ${error?.message}`);
  } finally {
    sleep(waitTimeAfterVu);
  }
}

export function createOutSideCibTransfer(
  type: "load" | "stress" | "endurance" | "smoke" | "shock",
) {
  const targetVUs = getTargetVus(type, "createOutSideCibTransfer");
  switch (type) {
    case "load":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "15m", target: targetVUs },
          { duration: "50m", target: targetVUs },
        ],
        exec: createTransferOutsideCIBExec.name,
      };
    case "endurance":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        stages: [
          { duration: "1h", target: targetVUs },
          { duration: "12h", target: targetVUs },
          { duration: "1h", target: 0 },
        ],
        exec: createTransferOutsideCIBExec.name,
      };
    case "smoke":
      return {
        executor: "per-vu-iterations",
        vus: 1,
        iterations: 1,
        exec: createTransferOutsideCIBExec.name,
      };
    case "shock":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "5m", target: targetVUs },
          { duration: "5m", target: targetVUs },
        ],
        exec: createTransferOutsideCIBExec.name,
      };
    default:
      throw new Error("Invalid type");
  }
}
